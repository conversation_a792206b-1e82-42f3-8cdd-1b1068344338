@echo off
echo ========================================
echo    wnbios_dll 锟斤拷锟斤拷锟斤拷锟斤拷疟锟� (VS2022)
echo ========================================
echo.

REM 锟斤拷锟斤拷欠锟斤拷锟絍S锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷示锟斤拷锟斤拷
if "%VSINSTALLDIR%"=="" (
    echo 锟斤拷锟斤拷: 锟斤拷锟斤拷 Visual Studio 2022 锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷示锟斤拷锟斤拷锟斤拷锟叫此脚憋拷
    echo.
    echo 锟斤拷锟斤拷锟斤拷锟斤拷:
    echo 1. 锟津开匡拷始锟剿碉拷
    echo 2. 锟斤拷锟斤拷 "Developer Command Prompt for VS 2022"
    echo 3. 锟皆癸拷锟斤拷员锟斤拷锟斤拷锟斤拷锟斤拷
    echo 4. 锟斤拷锟斤拷锟斤拷锟斤拷目录锟斤拷锟斤拷锟斤拷 build.bat
    echo.
    pause
    exit /b 1
)

echo 锟斤拷獾� Visual Studio 锟斤拷锟斤拷: %VSINSTALLDIR%
echo.

REM 锟斤拷锟斤拷锟斤拷员权锟斤拷
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 锟斤拷锟斤拷: 锟斤拷锟斤拷锟皆癸拷锟斤拷员权锟斤拷锟斤拷锟斤拷锟斤拷确锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷
    echo.
)

REM 锟斤拷锟� CMake
cmake --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 锟斤拷锟斤拷: 未锟揭碉拷 CMake
    echo 锟诫安装 CMake 锟斤拷确锟斤拷锟斤拷锟斤拷 PATH 锟斤拷
    pause
    exit /b 1
)

echo CMake 锟芥本:
cmake --version | findstr "cmake version"
echo.

REM 锟斤拷锟斤拷锟缴的癸拷锟斤拷锟侥硷拷
if exist build (
    echo 锟斤拷锟斤拷锟缴的癸拷锟斤拷锟侥硷拷...
    rmdir /s /q build
)

REM 锟斤拷锟斤拷锟斤拷锟斤拷目录
mkdir build
cd build

echo 锟斤拷锟斤拷锟斤拷锟斤拷 Visual Studio 2022 锟斤拷目锟侥硷拷...
cmake .. -G "Visual Studio 17 2022" -A x64

if %errorlevel% neq 0 (
    echo CMake 锟斤拷锟斤拷失锟斤拷
    cd ..
    pause
    exit /b 1
)

echo.
echo 锟斤拷锟节憋拷锟斤拷锟斤拷目 (Release 锟斤拷锟斤拷)...
cmake --build . --config Release --parallel

if %errorlevel% neq 0 (
    echo 锟斤拷锟斤拷失锟斤拷
    cd ..
    pause
    exit /b 1
)

echo.
echo 锟斤拷锟斤拷晒锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟侥硷拷...

REM 锟斤拷锟狡憋拷要锟侥硷拷
cd ..
if not exist dist mkdir dist
if not exist dist\include mkdir dist\include
if not exist dist\lib mkdir dist\lib
if not exist dist\bin mkdir dist\bin
if not exist dist\examples mkdir dist\examples
if not exist dist\docs mkdir dist\docs

copy /Y build\bin\Release\wnbios_dll.dll dist\bin\ >nul
copy /Y build\lib\Release\wnbios_dll.lib dist\lib\ >nul
copy /Y include\wnbios_dll.h dist\include\ >nul
copy /Y include\wnbios_types.h dist\include\ >nul
copy /Y build\bin\Release\c_example.exe dist\examples\ >nul
copy /Y build\bin\Release\data_types_example.exe dist\examples\ >nul
copy /Y build\bin\Release\macro_usage_example.exe dist\examples\ >nul
copy /Y examples\c_example.c dist\examples\ >nul
copy /Y examples\data_types_example.c dist\examples\ >nul
copy /Y examples\macro_usage_example.c dist\examples\ >nul
copy /Y README.md dist\ >nul
copy /Y docs\API_Documentation.md dist\docs\ >nul
copy /Y docs\Quick_Start_Guide.md dist\docs\ >nul
copy /Y docs\Macro_Usage_Guide.md dist\docs\ >nul

echo.
echo ========================================
echo           锟斤拷锟斤拷锟斤拷桑锟�
echo ========================================
echo.
echo 杈撳嚭鏂囦欢浣嶄簬 dist 鐩綍锛�
echo   馃搧 dist\bin\
echo      鈹溾攢鈹€ wnbios_dll.dll     (鍔ㄦ€侀摼鎺ュ簱)
echo   馃搧 dist\lib\
echo      鈹溾攢鈹€ wnbios_dll.lib     (瀵煎叆搴�)
echo   馃搧 dist\include\
echo      鈹溾攢鈹€ wnbios_dll.h       (涓昏API澶存枃浠�)
echo      鈹斺攢鈹€ wnbios_types.h     (渚挎嵎瀹忓畾涔�)
echo   馃搧 dist\examples\
echo      鈹溾攢鈹€ c_example.exe      (鍩烘湰绀轰緥绋嬪簭)
echo      鈹溾攢鈹€ data_types_example.exe (鏁版嵁绫诲瀷绀轰緥)
echo      鈹溾攢鈹€ macro_usage_example.exe (瀹忎娇鐢ㄧず渚�)
echo      鈹斺攢鈹€ 瀵瑰簲鐨勬簮浠ｇ爜鏂囦欢
echo   馃搧 dist\docs\
echo      鈹溾攢鈹€ API_Documentation.md (瀹屾暣API鏂囨。)
echo      鈹溾攢鈹€ Quick_Start_Guide.md (蹇€熷叆闂ㄦ寚鍗�)
echo      鈹斺攢鈹€ Macro_Usage_Guide.md (渚挎嵎瀹忎娇鐢ㄦ寚鍗�)
echo   馃搫 README.md              (椤圭洰璇存槑)
echo.
echo 馃殌 蹇€熸祴璇�:
echo    cd dist\examples
echo    c_example.exe
echo    data_types_example.exe
echo    macro_usage_example.exe
echo.
echo 馃摎 鏌ョ湅鏂囨。:
echo    type ..\README.md
echo    type ..\docs\Quick_Start_Guide.md
echo    type ..\docs\Macro_Usage_Guide.md
echo.
echo ??  注锟斤拷: 锟斤拷锟斤拷示锟斤拷锟斤拷锟斤拷锟斤拷要锟斤拷锟斤拷员权锟斤拷
echo.
pause