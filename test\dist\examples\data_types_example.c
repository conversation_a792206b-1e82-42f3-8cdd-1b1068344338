#include <stdio.h>
#include <stdlib.h>
#include <windows.h>
#include "../include/wnbios_dll.h"

// 示例：如何读取不同类型的数据

int main() {
    printf("=== wnbios_dll 数据类型读取示例 ===\n\n");
    
    // 初始化驱动
    if (wnbios_init_driver() != WNBIOS_SUCCESS) {
        printf("驱动初始化失败\n");
        return 1;
    }
    
    // 设置目标进程（以notepad.exe为例）
    if (wnbios_set_target_process("notepad.exe") != WNBIOS_SUCCESS) {
        printf("未找到目标进程，请先启动notepad.exe\n");
        wnbios_cleanup_driver();
        return 1;
    }
    
    // 获取进程基址
    uintptr_t base_address = wnbios_get_process_base("notepad.exe");
    if (base_address == 0) {
        printf("获取进程基址失败\n");
        wnbios_cleanup_driver();
        return 1;
    }
    
    printf("进程基址: 0x%llx\n\n", base_address);
    
    // ================================
    // 1. 读取基本数据类型
    // ================================
    
    // 读取单个字节
    uint8_t byte_value;
    if (wnbios_read_memory(base_address, &byte_value, sizeof(byte_value)) > 0) {
        printf("字节值 (uint8_t): 0x%02X\n", byte_value);
    }
    
    // 读取16位整数
    uint16_t word_value;
    if (wnbios_read_memory(base_address, &word_value, sizeof(word_value)) > 0) {
        printf("16位值 (uint16_t): 0x%04X (%u)\n", word_value, word_value);
    }
    
    // 读取32位整数
    uint32_t dword_value;
    if (wnbios_read_memory(base_address, &dword_value, sizeof(dword_value)) > 0) {
        printf("32位值 (uint32_t): 0x%08X (%u)\n", dword_value, dword_value);
    }
    
    // 读取64位整数
    uint64_t qword_value;
    if (wnbios_read_memory(base_address, &qword_value, sizeof(qword_value)) > 0) {
        printf("64位值 (uint64_t): 0x%016llX (%llu)\n", qword_value, qword_value);
    }
    
    // 读取浮点数
    float float_value;
    if (wnbios_read_memory(base_address + 0x100, &float_value, sizeof(float_value)) > 0) {
        printf("浮点数 (float): %f\n", float_value);
    }
    
    // 读取双精度浮点数
    double double_value;
    if (wnbios_read_memory(base_address + 0x108, &double_value, sizeof(double_value)) > 0) {
        printf("双精度 (double): %lf\n", double_value);
    }
    
    // 读取指针
    uintptr_t pointer_value;
    if (wnbios_read_memory(base_address + 0x8, &pointer_value, sizeof(pointer_value)) > 0) {
        printf("指针值 (uintptr_t): 0x%llx\n", pointer_value);
    }
    
    printf("\n");
    
    // ================================
    // 2. 读取字符串
    // ================================
    
    // 读取ANSI字符串
    char ansi_string[256] = {0};
    if (wnbios_read_memory(base_address + 0x200, ansi_string, sizeof(ansi_string) - 1) > 0) {
        printf("ANSI字符串: %s\n", ansi_string);
    }
    
    // 读取Unicode字符串
    wchar_t unicode_string[128] = {0};
    if (wnbios_read_memory(base_address + 0x300, unicode_string, sizeof(unicode_string) - sizeof(wchar_t)) > 0) {
        wprintf(L"Unicode字符串: %s\n", unicode_string);
    }
    
    printf("\n");
    
    // ================================
    // 3. 读取数组
    // ================================
    
    // 读取整数数组
    int int_array[10];
    if (wnbios_read_memory(base_address + 0x400, int_array, sizeof(int_array)) > 0) {
        printf("整数数组: ");
        for (int i = 0; i < 10; i++) {
            printf("%d ", int_array[i]);
        }
        printf("\n");
    }
    
    // 读取字节数组
    uint8_t byte_array[16];
    if (wnbios_read_memory(base_address, byte_array, sizeof(byte_array)) > 0) {
        printf("字节数组: ");
        for (int i = 0; i < 16; i++) {
            printf("%02X ", byte_array[i]);
        }
        printf("\n");
    }
    
    printf("\n");
    
    // ================================
    // 4. 读取结构体
    // ================================
    
    // 定义一个示例结构体
    typedef struct {
        int id;
        float x, y, z;
        char name[32];
    } GameEntity;
    
    GameEntity entity;
    if (wnbios_read_memory(base_address + 0x500, &entity, sizeof(entity)) > 0) {
        printf("结构体数据:\n");
        printf("  ID: %d\n", entity.id);
        printf("  坐标: (%.2f, %.2f, %.2f)\n", entity.x, entity.y, entity.z);
        printf("  名称: %s\n", entity.name);
    }
    
    printf("\n");
    
    // ================================
    // 5. 读取指针链
    // ================================
    
    // 读取多级指针
    uintptr_t ptr1, ptr2, final_value;
    
    // 第一级指针
    if (wnbios_read_memory(base_address + 0x10, &ptr1, sizeof(ptr1)) > 0) {
        printf("第一级指针: 0x%llx\n", ptr1);
        
        // 第二级指针
        if (wnbios_read_memory(ptr1 + 0x8, &ptr2, sizeof(ptr2)) > 0) {
            printf("第二级指针: 0x%llx\n", ptr2);
            
            // 最终值
            if (wnbios_read_memory(ptr2 + 0x4, &final_value, sizeof(final_value)) > 0) {
                printf("最终值: 0x%llx (%llu)\n", final_value, final_value);
            }
        }
    }
    
    printf("\n");
    
    // ================================
    // 6. 批量读取不同类型
    // ================================
    
    // 准备批量读取的地址和缓冲区
    uintptr_t addresses[3] = {
        base_address,           // 读取DWORD
        base_address + 0x4,     // 读取QWORD
        base_address + 0xC      // 读取字符串
    };
    
    uint32_t value1;
    uint64_t value2;
    char string_value[64];
    
    void* buffers[3] = { &value1, &value2, string_value };
    size_t sizes[3] = { sizeof(value1), sizeof(value2), sizeof(string_value) - 1 };
    
    if (wnbios_read_memory_batch(addresses, buffers, sizes, 3) == WNBIOS_SUCCESS) {
        printf("批量读取结果:\n");
        printf("  值1 (DWORD): 0x%08X\n", value1);
        printf("  值2 (QWORD): 0x%016llX\n", value2);
        printf("  字符串: %s\n", string_value);
    }
    
    // 清理
    wnbios_cleanup_driver();
    
    printf("\n按任意键退出...\n");
    getchar();
    
    return 0;
}