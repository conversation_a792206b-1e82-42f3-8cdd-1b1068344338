#include <stdio.h>
#include <stdlib.h>
#include <windows.h>
#include "../include/wnbios_dll.h"
#include "../include/wnbios_types.h"

// 使用便捷宏读取数据的示例

int main() {
    printf("=== wnbios_dll 便捷宏使用示例 ===\n\n");
    
    // 初始化驱动
    if (wnbios_init_driver() != WNBIOS_SUCCESS) {
        printf("驱动初始化失败\n");
        return 1;
    }
    
    // 设置目标进程
    if (wnbios_set_target_process("notepad.exe") != WNBIOS_SUCCESS) {
        printf("未找到目标进程，请先启动notepad.exe\n");
        wnbios_cleanup_driver();
        return 1;
    }
    
    uintptr_t base_address = wnbios_get_process_base("notepad.exe");
    if (base_address == 0) {
        printf("获取进程基址失败\n");
        wnbios_cleanup_driver();
        return 1;
    }
    
    printf("进程基址: 0x%llx\n\n", base_address);
    
    // ================================
    // 使用便捷宏读取基本数据类型
    // ================================
    
    uint8_t byte_val;
    uint16_t word_val;
    uint32_t dword_val;
    uint64_t qword_val;
    float float_val;
    double double_val;
    uintptr_t ptr_val;
    
    if (READ_BYTE(base_address, &byte_val)) {
        printf("字节值: 0x%02X\n", byte_val);
    }
    
    if (READ_WORD(base_address, &word_val)) {
        printf("16位值: 0x%04X\n", word_val);
    }
    
    if (READ_DWORD(base_address, &dword_val)) {
        printf("32位值: 0x%08X\n", dword_val);
    }
    
    if (READ_QWORD(base_address, &qword_val)) {
        printf("64位值: 0x%016llX\n", qword_val);
    }
    
    if (READ_POINTER(base_address + 0x8, &ptr_val)) {
        printf("指针值: 0x%llx\n", ptr_val);
    }
    
    printf("\n");
    
    // ================================
    // 读取游戏相关数据结构
    // ================================
    
    // 读取3D向量
    Vector3 position;
    if (READ_STRUCT(base_address + 0x100, &position, Vector3)) {
        printf("3D位置: (%.2f, %.2f, %.2f)\n", position.x, position.y, position.z);
    }
    
    // 读取颜色
    Color32 color;
    if (READ_STRUCT(base_address + 0x200, &color, Color32)) {
        printf("颜色: R=%d G=%d B=%d A=%d\n", color.r, color.g, color.b, color.a);
    }
    
    // 读取玩家信息
    PlayerInfo player;
    if (READ_STRUCT(base_address + 0x300, &player, PlayerInfo)) {
        printf("玩家信息:\n");
        printf("  ID: %u\n", player.player_id);
        printf("  名称: %s\n", player.name);
        printf("  位置: (%.2f, %.2f, %.2f)\n", player.position.x, player.position.y, player.position.z);
        printf("  生命值: %.1f\n", player.health);
        printf("  护甲: %.1f\n", player.armor);
        printf("  分数: %u\n", player.score);
        printf("  队伍: %u\n", player.team);
    }
    
    printf("\n");
    
    // ================================
    // 读取数组
    // ================================
    
    // 读取整数数组
    int int_array[5];
    if (READ_ARRAY(base_address + 0x400, int_array, 5, int)) {
        printf("整数数组: ");
        for (int i = 0; i < 5; i++) {
            printf("%d ", int_array[i]);
        }
        printf("\n");
    }
    
    // 读取Vector3数组
    Vector3 positions[3];
    if (READ_ARRAY(base_address + 0x500, positions, 3, Vector3)) {
        printf("位置数组:\n");
        for (int i = 0; i < 3; i++) {
            printf("  [%d]: (%.2f, %.2f, %.2f)\n", i, positions[i].x, positions[i].y, positions[i].z);
        }
    }
    
    printf("\n");
    
    // ================================
    // 使用指针链读取
    // ================================
    
    // 读取二级指针链
    uint32_t final_value;
    if (read_pointer_chain_2(base_address, 0x10, 0x8, &final_value, sizeof(final_value))) {
        printf("二级指针链结果: %u (0x%08X)\n", final_value, final_value);
    }
    
    // 读取三级指针链
    float health_value;
    if (read_pointer_chain_3(base_address, 0x20, 0x18, 0x4, &health_value, sizeof(health_value))) {
        printf("三级指针链结果 (生命值): %.1f\n", health_value);
    }
    
    printf("\n");
    
    // ================================
    // 写入数据示例
    // ================================
    
    printf("写入数据示例:\n");
    
    // 写入基本类型
    if (WRITE_DWORD(base_address + 0x1000, 0x12345678)) {
        printf("成功写入DWORD值\n");
    }
    
    if (WRITE_FLOAT(base_address + 0x1004, 3.14159f)) {
        printf("成功写入浮点数\n");
    }
    
    // 写入结构体
    Vector3 new_position = {100.0f, 200.0f, 300.0f};
    if (WRITE_STRUCT(base_address + 0x1008, &new_position, Vector3)) {
        printf("成功写入3D向量\n");
    }
    
    // 写入字符串
    char message[] = "Hello from wnbios!";
    if (WRITE_STRING(base_address + 0x1020, message, strlen(message) + 1)) {
        printf("成功写入字符串\n");
    }
    
    printf("\n");
    
    // ================================
    // 验证写入的数据
    // ================================
    
    printf("验证写入的数据:\n");
    
    uint32_t read_dword;
    if (READ_DWORD(base_address + 0x1000, &read_dword)) {
        printf("读取的DWORD: 0x%08X\n", read_dword);
    }
    
    float read_float;
    if (READ_FLOAT(base_address + 0x1004, &read_float)) {
        printf("读取的浮点数: %.5f\n", read_float);
    }
    
    Vector3 read_position;
    if (READ_STRUCT(base_address + 0x1008, &read_position, Vector3)) {
        printf("读取的3D向量: (%.1f, %.1f, %.1f)\n", read_position.x, read_position.y, read_position.z);
    }
    
    char read_message[64] = {0};
    if (READ_STRING(base_address + 0x1020, read_message, sizeof(read_message) - 1)) {
        printf("读取的字符串: %s\n", read_message);
    }
    
    // 清理
    wnbios_cleanup_driver();
    
    printf("\n按任意键退出...\n");
    getchar();
    
    return 0;
}