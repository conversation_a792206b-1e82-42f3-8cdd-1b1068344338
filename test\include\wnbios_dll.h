#pragma once

#ifndef WNBIOS_DLL_H
#define WNBIOS_DLL_H

#ifdef __cplusplus
extern "C" {
#endif

#include <windows.h>
#include <stdint.h>

// DLL导出宏定义
#ifdef WNBIOS_DLL_EXPORTS
#define WNBIOS_API __declspec(dllexport)
#else
#define WNBIOS_API __declspec(dllimport)
#endif

// 调用约定
#define WNBIOS_CALL __stdcall

// 错误码定义
#define WNBIOS_SUCCESS           0
#define WNBIOS_ERROR_INIT        -1
#define WNBIOS_ERROR_DRIVER      -2
#define WNBIOS_ERROR_PROCESS     -3
#define WNBIOS_ERROR_MEMORY      -4
#define WNBIOS_ERROR_MODULE      -5
#define WNBIOS_ERROR_INVALID_ARG -6
#define WNBIOS_ERROR_BUFFER_SIZE -7
#define WNBIOS_ERROR_ACCESS      -8
#define WNBIOS_ERROR_NOT_FOUND   -9
#define WNBIOS_ERROR_TIMEOUT     -10

// 数据结构定义
typedef struct {
    char name[256];           // 进程名称
    DWORD pid;               // 进程ID
    uintptr_t base_address;  // 进程基址
    uintptr_t cr3;           // 页目录基址
} WnbiosProcessInfo;

typedef struct {
    wchar_t name[256];       // 模块名称
    uintptr_t base_address;  // 模块基址
    DWORD size;              // 模块大小
    uintptr_t entry_point;   // 入口点地址
} WnbiosModuleInfo;

typedef struct {
    int error_code;          // 错误码
    char error_message[512]; // 错误描述
    char function_name[128]; // 发生错误的函数名
    DWORD thread_id;         // 线程ID
} WnbiosErrorInfo;

// 日志级别定义
typedef enum {
    WNBIOS_LOG_ERROR = 0,
    WNBIOS_LOG_WARNING = 1,
    WNBIOS_LOG_INFO = 2,
    WNBIOS_LOG_DEBUG = 3
} WnbiosLogLevel;

// 日志回调函数类型
typedef void (WNBIOS_CALL *WnbiosLogCallback)(WnbiosLogLevel level, const char* message);

// ================================
// 驱动管理API
// ================================

/**
 * 初始化wnbios驱动
 * @return 成功返回WNBIOS_SUCCESS，失败返回错误码
 */
WNBIOS_API int WNBIOS_CALL wnbios_init_driver(void);

/**
 * 清理wnbios驱动
 * @return 成功返回WNBIOS_SUCCESS，失败返回错误码
 */
WNBIOS_API int WNBIOS_CALL wnbios_cleanup_driver(void);

/**
 * 检查驱动是否已加载
 * @return 1表示已加载，0表示未加载，负数表示错误
 */
WNBIOS_API int WNBIOS_CALL wnbios_is_driver_loaded(void);

// ================================
// 进程操作API
// ================================



/**
 * 根据名称查找进程
 * @param name 进程名称
 * @param process 输出的进程信息
 * @return 成功返回WNBIOS_SUCCESS，失败返回错误码
 */
WNBIOS_API int WNBIOS_CALL wnbios_find_process_by_name(const char* name, WnbiosProcessInfo* process);

/**
 * 获取进程基址
 * @param process_name 进程名称
 * @return 成功返回进程基址，失败返回0
 */
WNBIOS_API uintptr_t WNBIOS_CALL wnbios_get_process_base(const char* process_name);

/**
 * 设置目标进程（用于后续的内存操作）
 * @param process_name 进程名称
 * @return 成功返回WNBIOS_SUCCESS，失败返回错误码
 */
WNBIOS_API int WNBIOS_CALL wnbios_set_target_process(const char* process_name);

// ================================
// 内存操作API
// ================================

/**
 * 读取虚拟内存
 * @param address 内存地址
 * @param buffer 输出缓冲区
 * @param size 读取大小
 * @return 成功返回实际读取的字节数，失败返回负数错误码
 */
WNBIOS_API int WNBIOS_CALL wnbios_read_memory(uintptr_t address, void* buffer, size_t size);

/**
 * 写入虚拟内存
 * @param address 内存地址
 * @param data 要写入的数据
 * @param size 写入大小
 * @return 成功返回实际写入的字节数，失败返回负数错误码
 */
WNBIOS_API int WNBIOS_CALL wnbios_write_memory(uintptr_t address, const void* data, size_t size);

/**
 * 批量读取内存
 * @param addresses 地址数组
 * @param buffers 输出缓冲区数组
 * @param sizes 每个地址的读取大小数组
 * @param count 地址数量
 * @return 成功返回WNBIOS_SUCCESS，失败返回错误码
 */
WNBIOS_API int WNBIOS_CALL wnbios_read_memory_batch(const uintptr_t* addresses, void** buffers, const size_t* sizes, int count);

// ================================
// 模块操作API
// ================================

/**
 * 枚举进程模块
 * @param process_name 进程名称
 * @param modules 模块信息数组缓冲区
 * @param count 输入时为缓冲区大小，输出时为实际模块数量
 * @return 成功返回WNBIOS_SUCCESS，失败返回错误码
 */
WNBIOS_API int WNBIOS_CALL wnbios_enum_modules(const char* process_name, WnbiosModuleInfo* modules, int* count);

/**
 * 查找模块基址
 * @param process_name 进程名称
 * @param module_name 模块名称（宽字符）
 * @return 成功返回模块基址，失败返回0
 */
WNBIOS_API uintptr_t WNBIOS_CALL wnbios_find_module_base(const char* process_name, const wchar_t* module_name);

// ================================
// 错误处理API
// ================================

/**
 * 获取最后一次错误信息
 * @param error_info 输出的错误信息
 * @return 成功返回WNBIOS_SUCCESS，失败返回错误码
 */
WNBIOS_API int WNBIOS_CALL wnbios_get_last_error(WnbiosErrorInfo* error_info);

/**
 * 清除错误状态
 */
WNBIOS_API void WNBIOS_CALL wnbios_clear_error(void);

/**
 * 获取错误码对应的描述字符串
 * @param error_code 错误码
 * @return 错误描述字符串
 */
WNBIOS_API const char* WNBIOS_CALL wnbios_get_error_string(int error_code);

// ================================
// 日志和调试API
// ================================

/**
 * 设置日志回调函数
 * @param callback 日志回调函数
 */
WNBIOS_API void WNBIOS_CALL wnbios_set_log_callback(WnbiosLogCallback callback);

/**
 * 设置日志级别
 * @param level 日志级别
 */
WNBIOS_API void WNBIOS_CALL wnbios_set_log_level(WnbiosLogLevel level);

/**
 * 启用或禁用调试模式
 * @param enable 1启用，0禁用
 */
WNBIOS_API void WNBIOS_CALL wnbios_set_debug_mode(int enable);

// ================================
// 实用工具API
// ================================

/**
 * 获取DLL版本信息
 * @param major 主版本号
 * @param minor 次版本号
 * @param patch 补丁版本号
 */
WNBIOS_API void WNBIOS_CALL wnbios_get_version(int* major, int* minor, int* patch);

/**
 * 获取支持的Windows版本信息
 * @return 版本信息字符串
 */
WNBIOS_API const char* WNBIOS_CALL wnbios_get_supported_versions(void);

#ifdef __cplusplus
}
#endif

#endif // WNBIOS_DLL_H