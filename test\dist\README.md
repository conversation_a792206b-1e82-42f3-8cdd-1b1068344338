# WNBIOS_DLL

一个基于 Windows 内核驱动的高性能内存操作库，提供跨进程内存读写、模块管理等功能。

## ✨ 特性

- 🔧 **内核级访问** - 通过嵌入式驱动实现内核级内存操作
- 🛡️ **线程安全** - 内置互斥锁保护，支持多线程环境
- 🎯 **类型灵活** - 支持读写任意数据类型（基本类型、结构体、数组等）
- 📝 **完善日志** - 可配置的日志级别和回调机制
- ❌ **错误处理** - 详细的错误码和错误信息系统
- 🚀 **便捷宏** - 提供简化常用操作的宏定义

## 🎯 适用场景

- 游戏外挂开发
- 系统监控工具
- 进程分析软件
- 内存调试工具
- 安全研究

## 🏗️ 项目结构

```
test/
├── include/                 # 头文件
│   ├── wnbios_dll.h        # 主要API接口
│   └── wnbios_types.h      # 便捷宏和类型定义
├── src/                    # 源代码
│   ├── wnbios_dll.cpp      # 主要实现
│   ├── drv.h               # 驱动相关
│   └── wnbios_dll.def      # DLL导出定义
├── examples/               # 示例代码
│   ├── c_example.c         # 基本使用示例
│   ├── data_types_example.c # 数据类型示例
│   └── macro_usage_example.c # 宏使用示例
├── docs/                   # 文档
│   ├── API_Documentation.md # 完整API文档
│   └── Quick_Start_Guide.md # 快速入门指南
├── CMakeLists.txt          # CMake配置
└── build.bat              # Windows构建脚本
```

## 🚀 快速开始

### 环境要求

- Windows 10/11 (Build 17763+)
- Visual Studio 2022
- CMake 3.16+
- 管理员权限

### 编译

```bash
# 克隆项目
git clone <repository-url>
cd test

# 使用构建脚本（推荐）
build.bat

# 或者手动编译
mkdir build && cd build
cmake .. -G "Visual Studio 17 2022" -A x64
cmake --build . --config Release
```

### 基本使用

```c
#include "wnbios_dll.h"

int main() {
    // 1. 初始化驱动
    if (wnbios_init_driver() != WNBIOS_SUCCESS) {
        printf("驱动初始化失败\n");
        return -1;
    }
    
    // 2. 设置目标进程
    if (wnbios_set_target_process("notepad.exe") != WNBIOS_SUCCESS) {
        printf("未找到目标进程\n");
        wnbios_cleanup_driver();
        return -1;
    }
    
    // 3. 读取内存
    uint32_t value;
    if (wnbios_read_memory(0x12345678, &value, sizeof(value)) > 0) {
        printf("读取的值: %u\n", value);
    }
    
    // 4. 写入内存
    uint32_t new_value = 9999;
    wnbios_write_memory(0x12345678, &new_value, sizeof(new_value));
    
    // 5. 清理资源
    wnbios_cleanup_driver();
    return 0;
}
```

### 使用便捷宏（可选）

为了简化开发，项目提供了 `wnbios_types.h` 头文件：

```c
#include "wnbios_dll.h"      // 基础API
#include "wnbios_types.h"    // 便捷宏和游戏数据结构

// 读取数据（简化版）
uint32_t health;
READ_DWORD(health_addr, &health);

// 写入数据（更直观）
WRITE_DWORD(health_addr, 9999);
WRITE_FLOAT(speed_addr, 10.5f);

// 使用预定义的游戏数据结构
Vector3 position;           // 3D向量
PlayerInfo player;          // 玩家信息结构
READ_STRUCT(player_addr, &player, PlayerInfo);

// 指针链读取（游戏外挂常用）
float health;
read_pointer_chain_3(base_addr, 0x100, 0x50, 0x10, &health, sizeof(health));
```

**便捷宏的优势：**
- 🎯 **更简洁** - 减少重复代码
- 🛡️ **类型安全** - 自动计算数据大小
- 🎮 **游戏友好** - 预定义常用数据结构
- 📝 **易读性** - 宏名称直观表达操作意图

## 📚 API 概览

### 驱动管理
- `wnbios_init_driver()` - 初始化驱动
- `wnbios_cleanup_driver()` - 清理驱动
- `wnbios_is_driver_loaded()` - 检查驱动状态

### 进程操作
- `wnbios_find_process_by_name()` - 查找进程
- `wnbios_get_process_base()` - 获取进程基址
- `wnbios_set_target_process()` - 设置目标进程

### 内存操作
- `wnbios_read_memory()` - 读取内存
- `wnbios_write_memory()` - 写入内存
- `wnbios_read_memory_batch()` - 批量读取

### 模块管理
- `wnbios_enum_modules()` - 枚举模块
- `wnbios_find_module_base()` - 查找模块基址

### 错误处理
- `wnbios_get_last_error()` - 获取错误信息
- `wnbios_clear_error()` - 清除错误状态
- `wnbios_get_error_string()` - 获取错误描述

### 日志调试
- `wnbios_set_log_callback()` - 设置日志回调
- `wnbios_set_log_level()` - 设置日志级别
- `wnbios_set_debug_mode()` - 调试模式

## 📖 文档

- [完整API文档](docs/API_Documentation.md) - 详细的API说明和使用示例
- [快速入门指南](docs/Quick_Start_Guide.md) - 5分钟上手指南

## 🔧 示例代码

项目包含多个示例程序：

1. **基本示例** (`examples/c_example.c`)
   - 驱动初始化和清理
   - 进程查找和内存读写
   - 模块枚举

2. **数据类型示例** (`examples/data_types_example.c`)
   - 各种数据类型的读写
   - 结构体和数组操作
   - 指针链读取

3. **宏使用示例** (`examples/macro_usage_example.c`)
   - 便捷宏的使用方法
   - 游戏开发常用模式

运行示例：
```bash
cd dist/examples
c_example.exe
data_types_example.exe
macro_usage_example.exe
```

## ⚠️ 注意事项

1. **权限要求**：程序必须以管理员权限运行
2. **系统兼容性**：支持 Windows 10/11 (Build 17763+)
3. **目标进程**：使用内存操作前必须设置目标进程
4. **地址有效性**：确保内存地址有效，避免程序崩溃
5. **资源管理**：程序结束前务必调用清理函数

## 🛡️ 安全说明

本库仅供学习和研究使用，请遵守相关法律法规：

- 不要用于恶意软件开发
- 不要攻击他人的计算机系统
- 仅在自己拥有的软件上进行测试
- 遵守软件许可协议和服务条款

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题或建议，请通过以下方式联系：

- 提交 GitHub Issue
- 发送邮件至 [<EMAIL>]

---

**免责声明**：本软件仅供教育和研究目的使用。使用者需自行承担使用风险，开发者不对任何损失或法律后果负责。