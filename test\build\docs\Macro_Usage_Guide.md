# WNBIOS_TYPES.H 便捷宏使用指南

## 📖 概述

`wnbios_types.h` 是一个可选的头文件，提供了便捷宏和游戏开发常用的数据结构，让内存操作更加简洁和直观。

## 🎯 为什么使用便捷宏？

### 传统方式 vs 便捷宏对比

**传统方式（繁琐）：**
```c
#include "wnbios_dll.h"

// 读取玩家生命值
uint32_t health;
if (wnbios_read_memory(player_addr + 0x10, &health, sizeof(uint32_t)) > 0) {
    printf("生命值: %u\n", health);
}

// 修改生命值
uint32_t new_health = 9999;
wnbios_write_memory(player_addr + 0x10, &new_health, sizeof(uint32_t));

// 读取3D坐标
float x, y, z;
wnbios_read_memory(player_addr + 0x20, &x, sizeof(float));
wnbios_read_memory(player_addr + 0x24, &y, sizeof(float));
wnbios_read_memory(player_addr + 0x28, &z, sizeof(float));
printf("位置: (%.2f, %.2f, %.2f)\n", x, y, z);
```

**使用便捷宏（简洁）：**
```c
#include "wnbios_dll.h"
#include "wnbios_types.h"

// 读取玩家生命值
uint32_t health;
READ_DWORD(player_addr + 0x10, &health);
printf("生命值: %u\n", health);

// 修改生命值
WRITE_DWORD(player_addr + 0x10, 9999);

// 读取3D坐标（作为结构体）
Vector3 position;
READ_STRUCT(player_addr + 0x20, &position, Vector3);
printf("位置: (%.2f, %.2f, %.2f)\n", position.x, position.y, position.z);
```

## 📋 完整宏列表

### 基本数据类型读取宏

| 宏名称 | 数据类型 | 大小 | 示例 |
|--------|----------|------|------|
| `READ_BYTE(addr, out)` | `uint8_t` | 1字节 | `READ_BYTE(addr, &byte_val)` |
| `READ_WORD(addr, out)` | `uint16_t` | 2字节 | `READ_WORD(addr, &word_val)` |
| `READ_DWORD(addr, out)` | `uint32_t` | 4字节 | `READ_DWORD(addr, &dword_val)` |
| `READ_QWORD(addr, out)` | `uint64_t` | 8字节 | `READ_QWORD(addr, &qword_val)` |
| `READ_FLOAT(addr, out)` | `float` | 4字节 | `READ_FLOAT(addr, &float_val)` |
| `READ_DOUBLE(addr, out)` | `double` | 8字节 | `READ_DOUBLE(addr, &double_val)` |
| `READ_POINTER(addr, out)` | `uintptr_t` | 8字节(x64) | `READ_POINTER(addr, &ptr_val)` |

### 有符号整数读取宏

| 宏名称 | 数据类型 | 示例 |
|--------|----------|------|
| `READ_INT8(addr, out)` | `int8_t` | `READ_INT8(addr, &int8_val)` |
| `READ_INT16(addr, out)` | `int16_t` | `READ_INT16(addr, &int16_val)` |
| `READ_INT32(addr, out)` | `int32_t` | `READ_INT32(addr, &int32_val)` |
| `READ_INT64(addr, out)` | `int64_t` | `READ_INT64(addr, &int64_val)` |

### 基本数据类型写入宏

| 宏名称 | 示例 |
|--------|------|
| `WRITE_BYTE(addr, val)` | `WRITE_BYTE(addr, 255)` |
| `WRITE_WORD(addr, val)` | `WRITE_WORD(addr, 65535)` |
| `WRITE_DWORD(addr, val)` | `WRITE_DWORD(addr, 9999)` |
| `WRITE_QWORD(addr, val)` | `WRITE_QWORD(addr, 999999LL)` |
| `WRITE_FLOAT(addr, val)` | `WRITE_FLOAT(addr, 3.14f)` |
| `WRITE_DOUBLE(addr, val)` | `WRITE_DOUBLE(addr, 3.14159)` |
| `WRITE_POINTER(addr, val)` | `WRITE_POINTER(addr, new_ptr)` |

### 高级操作宏

| 宏名称 | 用途 | 示例 |
|--------|------|------|
| `READ_STRUCT(addr, out, type)` | 读取结构体 | `READ_STRUCT(addr, &player, Player)` |
| `WRITE_STRUCT(addr, data, type)` | 写入结构体 | `WRITE_STRUCT(addr, &player, Player)` |
| `READ_ARRAY(addr, out, count, type)` | 读取数组 | `READ_ARRAY(addr, scores, 10, int)` |
| `WRITE_ARRAY(addr, data, count, type)` | 写入数组 | `WRITE_ARRAY(addr, scores, 10, int)` |
| `READ_STRING(addr, out, size)` | 读取字符串 | `READ_STRING(addr, name, 64)` |
| `WRITE_STRING(addr, str, size)` | 写入字符串 | `WRITE_STRING(addr, "Hello", 6)` |

## 🎮 预定义游戏数据结构

### Vector3 - 3D向量
```c
typedef struct {
    float x, y, z;
} Vector3;

// 使用示例
Vector3 player_pos;
READ_STRUCT(player_addr + 0x10, &player_pos, Vector3);
printf("玩家位置: (%.2f, %.2f, %.2f)\n", player_pos.x, player_pos.y, player_pos.z);

// 传送玩家
Vector3 teleport_pos = {1000.0f, 0.0f, 1000.0f};
WRITE_STRUCT(player_addr + 0x10, &teleport_pos, Vector3);
```

### Vector2 - 2D向量
```c
typedef struct {
    float x, y;
} Vector2;

// 使用示例
Vector2 screen_pos;
READ_STRUCT(ui_addr, &screen_pos, Vector2);
```

### Color32 - 颜色结构
```c
typedef struct {
    uint8_t r, g, b, a;  // 红、绿、蓝、透明度
} Color32;

// 使用示例
Color32 player_color = {255, 0, 0, 255};  // 红色
WRITE_STRUCT(color_addr, &player_color, Color32);
```

### PlayerInfo - 玩家信息
```c
typedef struct {
    uint32_t player_id;
    char name[64];
    Vector3 position;
    float health;
    float armor;
    uint32_t score;
    uint32_t team;
} PlayerInfo;

// 使用示例
PlayerInfo player;
READ_STRUCT(player_base, &player, PlayerInfo);

printf("玩家信息:\n");
printf("  ID: %u\n", player.player_id);
printf("  名称: %s\n", player.name);
printf("  位置: (%.2f, %.2f, %.2f)\n", 
       player.position.x, player.position.y, player.position.z);
printf("  生命值: %.1f\n", player.health);
printf("  护甲: %.1f\n", player.armor);
printf("  分数: %u\n", player.score);
printf("  队伍: %u\n", player.team);
```

### GameEntity - 游戏实体
```c
typedef struct {
    uint32_t id;
    Vector3 position;
    Vector3 rotation;
    Vector3 scale;
    uint32_t flags;
} GameEntity;

// 使用示例
GameEntity entity;
READ_STRUCT(entity_addr, &entity, GameEntity);
```

### Matrix4x4 - 4x4矩阵
```c
typedef struct {
    float m[4][4];
} Matrix4x4;

// 使用示例
Matrix4x4 view_matrix;
READ_STRUCT(matrix_addr, &view_matrix, Matrix4x4);
```

## 🔗 指针链读取辅助函数

游戏中经常遇到多级指针，便捷函数可以简化操作：

### 函数定义
```c
// 二级指针链：Base -> Ptr1 -> Value
int read_pointer_chain_2(uintptr_t base, int offset1, int offset2, 
                        void* output, size_t size);

// 三级指针链：Base -> Ptr1 -> Ptr2 -> Value
int read_pointer_chain_3(uintptr_t base, int offset1, int offset2, int offset3,
                        void* output, size_t size);

// 四级指针链：Base -> Ptr1 -> Ptr2 -> Ptr3 -> Value
int read_pointer_chain_4(uintptr_t base, int offset1, int offset2, int offset3, int offset4,
                        void* output, size_t size);
```

### 使用示例

**场景：游戏中的指针链**
```
游戏基址 + 0x123456 -> 玩家指针 + 0x10 -> 属性指针 + 0x8 -> 生命值
```

**传统方式（繁琐）：**
```c
uintptr_t game_base = wnbios_get_process_base("game.exe");
uintptr_t player_ptr, attr_ptr;
float health;

// 第一级指针
if (READ_POINTER(game_base + 0x123456, &player_ptr)) {
    // 第二级指针
    if (READ_POINTER(player_ptr + 0x10, &attr_ptr)) {
        // 最终值
        if (READ_FLOAT(attr_ptr + 0x8, &health)) {
            printf("生命值: %.1f\n", health);
        }
    }
}
```

**使用指针链函数（简洁）：**
```c
uintptr_t game_base = wnbios_get_process_base("game.exe");
float health;

if (read_pointer_chain_3(game_base, 0x123456, 0x10, 0x8, &health, sizeof(health))) {
    printf("生命值: %.1f\n", health);
}
```

## 🚀 实战示例

### 示例1：FPS游戏外挂
```c
#include "wnbios_dll.h"
#include "wnbios_types.h"

int main() {
    // 初始化
    wnbios_init_driver();
    wnbios_set_target_process("fps_game.exe");
    
    uintptr_t game_base = wnbios_get_process_base("fps_game.exe");
    
    // 玩家数据指针链：游戏基址 -> 本地玩家 -> 属性
    uintptr_t player_base = game_base + 0x567890;
    
    // 读取当前状态
    PlayerInfo player;
    if (read_pointer_chain_2(player_base, 0x10, 0x0, &player, sizeof(player))) {
        printf("当前玩家: %s\n", player.name);
        printf("生命值: %.1f\n", player.health);
        printf("位置: (%.2f, %.2f, %.2f)\n", 
               player.position.x, player.position.y, player.position.z);
    }
    
    // 外挂功能
    printf("激活外挂功能...\n");
    
    // 1. 无敌模式
    float max_health = 9999.0f;
    if (read_pointer_chain_3(player_base, 0x10, 0x20, 0x4, &max_health, sizeof(max_health))) {
        printf("✓ 无敌模式已激活\n");
    }
    
    // 2. 无限弹药
    uint32_t max_ammo = 999;
    WRITE_DWORD(player_base + 0x50, max_ammo);  // 假设弹药偏移
    printf("✓ 无限弹药已激活\n");
    
    // 3. 飞行模式（修改Y坐标）
    Vector3 fly_pos = player.position;
    fly_pos.y += 100.0f;  // 向上飞行100单位
    WRITE_STRUCT(player_base + 0x30, &fly_pos, Vector3);
    printf("✓ 飞行模式已激活\n");
    
    // 4. 透视功能（修改敌人颜色）
    Color32 enemy_color = {255, 0, 0, 255};  // 红色高亮
    uintptr_t enemy_base = game_base + 0x888888;  // 假设敌人基址
    WRITE_STRUCT(enemy_base + 0x40, &enemy_color, Color32);
    printf("✓ 透视功能已激活\n");
    
    printf("外挂已激活，按任意键退出...\n");
    getchar();
    
    wnbios_cleanup_driver();
    return 0;
}
```

### 示例2：RPG游戏修改器
```c
#include "wnbios_dll.h"
#include "wnbios_types.h"

typedef struct {
    char name[32];
    uint32_t level;
    uint64_t experience;
    uint32_t gold;
    Vector3 position;
    struct {
        uint32_t strength;
        uint32_t agility;
        uint32_t intelligence;
        uint32_t vitality;
    } stats;
} RPGPlayer;

int main() {
    wnbios_init_driver();
    wnbios_set_target_process("rpg_game.exe");
    
    uintptr_t game_base = wnbios_get_process_base("rpg_game.exe");
    uintptr_t player_addr = game_base + 0x234567;
    
    // 读取角色信息
    RPGPlayer player;
    READ_STRUCT(player_addr, &player, RPGPlayer);
    
    printf("=== RPG 角色信息 ===\n");
    printf("角色名: %s\n", player.name);
    printf("等级: %u\n", player.level);
    printf("经验值: %llu\n", player.experience);
    printf("金币: %u\n", player.gold);
    printf("位置: (%.2f, %.2f, %.2f)\n", 
           player.position.x, player.position.y, player.position.z);
    printf("属性点:\n");
    printf("  力量: %u\n", player.stats.strength);
    printf("  敏捷: %u\n", player.stats.agility);
    printf("  智力: %u\n", player.stats.intelligence);
    printf("  体力: %u\n", player.stats.vitality);
    
    // 修改器功能
    printf("\n=== 激活修改器 ===\n");
    
    // 满级
    WRITE_DWORD(player_addr + offsetof(RPGPlayer, level), 99);
    
    // 满经验
    WRITE_QWORD(player_addr + offsetof(RPGPlayer, experience), 9999999ULL);
    
    // 无限金币
    WRITE_DWORD(player_addr + offsetof(RPGPlayer, gold), 999999);
    
    // 满属性点
    WRITE_DWORD(player_addr + offsetof(RPGPlayer, stats.strength), 999);
    WRITE_DWORD(player_addr + offsetof(RPGPlayer, stats.agility), 999);
    WRITE_DWORD(player_addr + offsetof(RPGPlayer, stats.intelligence), 999);
    WRITE_DWORD(player_addr + offsetof(RPGPlayer, stats.vitality), 999);
    
    printf("✓ 等级已设置为99\n");
    printf("✓ 经验值已最大化\n");
    printf("✓ 金币已设置为999999\n");
    printf("✓ 所有属性已最大化\n");
    
    wnbios_cleanup_driver();
    return 0;
}
```

### 示例3：内存监控工具
```c
#include "wnbios_dll.h"
#include "wnbios_types.h"
#include <windows.h>

int main() {
    wnbios_init_driver();
    wnbios_set_target_process("target_app.exe");
    
    uintptr_t monitor_addr = 0x12345678;  // 要监控的地址
    uint32_t last_value = 0;
    
    printf("开始监控地址 0x%llx 的值变化...\n", monitor_addr);
    printf("按 Ctrl+C 退出\n\n");
    
    while (1) {
        uint32_t current_value;
        if (READ_DWORD(monitor_addr, &current_value)) {
            if (current_value != last_value) {
                printf("[%02d:%02d:%02d] 值变化: %u -> %u (差值: %d)\n",
                       GetSystemTime().wHour,
                       GetSystemTime().wMinute, 
                       GetSystemTime().wSecond,
                       last_value, current_value, 
                       (int)current_value - (int)last_value);
                last_value = current_value;
            }
        }
        Sleep(100);  // 100ms检查一次
    }
    
    wnbios_cleanup_driver();
    return 0;
}
```

## ⚠️ 注意事项

1. **可选使用**：`wnbios_types.h` 完全可选，不影响基础功能
2. **包含顺序**：必须先包含 `wnbios_dll.h`，再包含 `wnbios_types.h`
3. **编译器兼容性**：在Visual Studio 2022中完全兼容
4. **性能影响**：宏在编译时展开，不会影响运行时性能
5. **调试友好**：宏展开后的代码便于调试和分析
6. **类型安全**：宏会自动处理数据类型大小，减少错误

## 🎯 最佳实践

1. **新手建议**：先学习基础API，熟悉后再使用便捷宏
2. **项目选择**：游戏外挂开发推荐使用便捷宏
3. **代码风格**：在同一项目中保持一致的使用方式
4. **错误处理**：便捷宏返回布尔值，记得检查返回值
5. **结构体对齐**：注意结构体内存对齐问题

---

*本指南涵盖了 `wnbios_types.h` 的所有功能和使用方法，帮助你更高效地进行内存操作开发。*