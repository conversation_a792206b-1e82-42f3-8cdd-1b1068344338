#pragma once

#ifndef WNBIOS_TYPES_H
#define WNBIOS_TYPES_H

#include "wnbios_dll.h"
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// ================================
// 便捷的数据类型读取宏
// ================================

// 读取基本数据类型的宏
#define READ_BYTE(addr, out)     (wnbios_read_memory(addr, out, sizeof(uint8_t)) > 0)
#define READ_WORD(addr, out)     (wnbios_read_memory(addr, out, sizeof(uint16_t)) > 0)
#define READ_DWORD(addr, out)    (wnbios_read_memory(addr, out, sizeof(uint32_t)) > 0)
#define READ_QWORD(addr, out)    (wnbios_read_memory(addr, out, sizeof(uint64_t)) > 0)
#define READ_FLOAT(addr, out)    (wnbios_read_memory(addr, out, sizeof(float)) > 0)
#define READ_DOUBLE(addr, out)   (wnbios_read_memory(addr, out, sizeof(double)) > 0)
#define READ_POINTER(addr, out)  (wnbios_read_memory(addr, out, sizeof(uintptr_t)) > 0)

// 读取有符号整数类型
#define READ_INT8(addr, out)     (wnbios_read_memory(addr, out, sizeof(int8_t)) > 0)
#define READ_INT16(addr, out)    (wnbios_read_memory(addr, out, sizeof(int16_t)) > 0)
#define READ_INT32(addr, out)    (wnbios_read_memory(addr, out, sizeof(int32_t)) > 0)
#define READ_INT64(addr, out)    (wnbios_read_memory(addr, out, sizeof(int64_t)) > 0)

// 读取字符串的宏
#define READ_STRING(addr, out, size) (wnbios_read_memory(addr, out, size) > 0)
#define READ_WSTRING(addr, out, size) (wnbios_read_memory(addr, out, size * sizeof(wchar_t)) > 0)

// 读取结构体的宏
#define READ_STRUCT(addr, out, type) (wnbios_read_memory(addr, out, sizeof(type)) > 0)

// 读取数组的宏
#define READ_ARRAY(addr, out, count, type) (wnbios_read_memory(addr, out, count * sizeof(type)) > 0)

// ================================
// 便捷的数据类型写入宏
// ================================

// 写入基本数据类型的宏
#define WRITE_BYTE(addr, val)    ({ uint8_t _v = val; wnbios_write_memory(addr, &_v, sizeof(uint8_t)) > 0; })
#define WRITE_WORD(addr, val)    ({ uint16_t _v = val; wnbios_write_memory(addr, &_v, sizeof(uint16_t)) > 0; })
#define WRITE_DWORD(addr, val)   ({ uint32_t _v = val; wnbios_write_memory(addr, &_v, sizeof(uint32_t)) > 0; })
#define WRITE_QWORD(addr, val)   ({ uint64_t _v = val; wnbios_write_memory(addr, &_v, sizeof(uint64_t)) > 0; })
#define WRITE_FLOAT(addr, val)   ({ float _v = val; wnbios_write_memory(addr, &_v, sizeof(float)) > 0; })
#define WRITE_DOUBLE(addr, val)  ({ double _v = val; wnbios_write_memory(addr, &_v, sizeof(double)) > 0; })
#define WRITE_POINTER(addr, val) ({ uintptr_t _v = val; wnbios_write_memory(addr, &_v, sizeof(uintptr_t)) > 0; })

// 写入有符号整数类型
#define WRITE_INT8(addr, val)    ({ int8_t _v = val; wnbios_write_memory(addr, &_v, sizeof(int8_t)) > 0; })
#define WRITE_INT16(addr, val)   ({ int16_t _v = val; wnbios_write_memory(addr, &_v, sizeof(int16_t)) > 0; })
#define WRITE_INT32(addr, val)   ({ int32_t _v = val; wnbios_write_memory(addr, &_v, sizeof(int32_t)) > 0; })
#define WRITE_INT64(addr, val)   ({ int64_t _v = val; wnbios_write_memory(addr, &_v, sizeof(int64_t)) > 0; })

// 写入字符串和结构体
#define WRITE_STRING(addr, str, size) (wnbios_write_memory(addr, str, size) > 0)
#define WRITE_STRUCT(addr, data, type) (wnbios_write_memory(addr, data, sizeof(type)) > 0)
#define WRITE_ARRAY(addr, data, count, type) (wnbios_write_memory(addr, data, count * sizeof(type)) > 0)

// ================================
// 指针链读取辅助函数
// ================================

// 读取多级指针的辅助函数声明
static inline int read_pointer_chain_2(uintptr_t base, int offset1, int offset2, void* output, size_t size) {
    uintptr_t ptr1, ptr2;
    if (!READ_POINTER(base + offset1, &ptr1)) return 0;
    if (!READ_POINTER(ptr1 + offset2, &ptr2)) return 0;
    return wnbios_read_memory(ptr2, output, size) > 0;
}

static inline int read_pointer_chain_3(uintptr_t base, int offset1, int offset2, int offset3, void* output, size_t size) {
    uintptr_t ptr1, ptr2, ptr3;
    if (!READ_POINTER(base + offset1, &ptr1)) return 0;
    if (!READ_POINTER(ptr1 + offset2, &ptr2)) return 0;
    if (!READ_POINTER(ptr2 + offset3, &ptr3)) return 0;
    return wnbios_read_memory(ptr3, output, size) > 0;
}

static inline int read_pointer_chain_4(uintptr_t base, int offset1, int offset2, int offset3, int offset4, void* output, size_t size) {
    uintptr_t ptr1, ptr2, ptr3, ptr4;
    if (!READ_POINTER(base + offset1, &ptr1)) return 0;
    if (!READ_POINTER(ptr1 + offset2, &ptr2)) return 0;
    if (!READ_POINTER(ptr2 + offset3, &ptr3)) return 0;
    if (!READ_POINTER(ptr3 + offset4, &ptr4)) return 0;
    return wnbios_read_memory(ptr4, output, size) > 0;
}

// ================================
// 常用游戏数据结构
// ================================

// 3D向量
typedef struct {
    float x, y, z;
} Vector3;

// 2D向量
typedef struct {
    float x, y;
} Vector2;

// 颜色结构
typedef struct {
    uint8_t r, g, b, a;
} Color32;

// 矩阵4x4
typedef struct {
    float m[4][4];
} Matrix4x4;

// 游戏实体基础结构
typedef struct {
    uint32_t id;
    Vector3 position;
    Vector3 rotation;
    Vector3 scale;
    uint32_t flags;
} GameEntity;

// 玩家信息结构
typedef struct {
    uint32_t player_id;
    char name[64];
    Vector3 position;
    float health;
    float armor;
    uint32_t score;
    uint32_t team;
} PlayerInfo;

#ifdef __cplusplus
}
#endif

#endif // WNBIOS_TYPES_H