# WNBIOS_DLL 快速入门指南

## 🚀 5分钟上手

### 1. 基本设置
```c
#include "wnbios_dll.h"

int main() {
    // 初始化驱动
    if (wnbios_init_driver() != WNBIOS_SUCCESS) {
        printf("驱动初始化失败\n");
        return -1;
    }
    
    // 设置目标进程
    if (wnbios_set_target_process("notepad.exe") != WNBIOS_SUCCESS) {
        printf("未找到目标进程\n");
        wnbios_cleanup_driver();
        return -1;
    }
    
    // 你的代码...
    
    // 清理资源
    wnbios_cleanup_driver();
    return 0;
}
```

### 2. 读取内存数据
```c
// 读取32位整数
uint32_t health;
if (wnbios_read_memory(0x12345678, &health, sizeof(health)) > 0) {
    printf("生命值: %u\n", health);
}

// 读取浮点数
float position_x;
wnbios_read_memory(player_addr + 0x10, &position_x, sizeof(position_x));

// 读取字符串
char player_name[64];
wnbios_read_memory(name_addr, player_name, sizeof(player_name) - 1);
player_name[63] = '\0';  // 确保字符串结束
```

### 3. 写入内存数据
```c
// 修改生命值
uint32_t new_health = 9999;
wnbios_write_memory(health_addr, &new_health, sizeof(new_health));

// 修改坐标
float new_x = 100.0f;
wnbios_write_memory(player_addr + 0x10, &new_x, sizeof(new_x));
```

### 4. 使用便捷宏（推荐）

为了简化开发，库提供了 `wnbios_types.h` 头文件，包含便捷宏：

```c
#include "wnbios_dll.h"      // 基础API
#include "wnbios_types.h"    // 便捷宏

// 读取数据（更简洁）
uint32_t health;
READ_DWORD(health_addr, &health);

float speed;
READ_FLOAT(speed_addr, &speed);

// 写入数据（更直观）
WRITE_DWORD(health_addr, 9999);
WRITE_FLOAT(speed_addr, 10.5f);

// 读取结构体
Vector3 position;  // 预定义的3D向量结构
READ_STRUCT(player_addr, &position, Vector3);

// 指针链读取（游戏中常见）
float final_value;
read_pointer_chain_3(base_addr, 0x10, 0x8, 0x4, &final_value, sizeof(final_value));
```

**可用的便捷宏：**
- `READ_BYTE`, `READ_WORD`, `READ_DWORD`, `READ_QWORD`
- `READ_FLOAT`, `READ_DOUBLE`, `READ_POINTER`
- `WRITE_BYTE`, `WRITE_WORD`, `WRITE_DWORD`, `WRITE_QWORD`
- `WRITE_FLOAT`, `WRITE_DOUBLE`, `WRITE_POINTER`
- `READ_STRUCT`, `WRITE_STRUCT` - 结构体操作
- `READ_ARRAY`, `WRITE_ARRAY` - 数组操作

### 5. 查找模块基址
```c
// 获取进程基址
uintptr_t base = wnbios_get_process_base("game.exe");

// 获取模块基址
uintptr_t engine_base = wnbios_find_module_base("game.exe", L"engine.dll");

// 计算最终地址
uintptr_t player_addr = engine_base + 0x123456;
```

## 🎯 常用模式

### 游戏外挂开发
```c
// 1. 初始化
wnbios_init_driver();
wnbios_set_target_process("game.exe");

// 2. 获取基址
uintptr_t base = wnbios_get_process_base("game.exe");
uintptr_t player_addr = base + 0x123456;  // 玩家数据偏移

// 3. 读取玩家数据
uint32_t health, armor;
READ_DWORD(player_addr + 0x10, &health);
READ_DWORD(player_addr + 0x14, &armor);

// 4. 修改数据
WRITE_DWORD(player_addr + 0x10, 9999);  // 无敌
WRITE_DWORD(player_addr + 0x14, 9999);  // 满护甲

// 5. 清理
wnbios_cleanup_driver();
```

### 内存监控
```c
// 监控内存变化
uint32_t old_value = 0;
while (1) {
    uint32_t current_value;
    if (READ_DWORD(monitor_addr, &current_value)) {
        if (current_value != old_value) {
            printf("值变化: %u -> %u\n", old_value, current_value);
            old_value = current_value;
        }
    }
    Sleep(100);  // 100ms检查一次
}
```

## ⚠️ 重要提醒

1. **管理员权限**：程序必须以管理员身份运行
2. **目标进程**：确保目标进程正在运行
3. **地址有效性**：使用正确的内存地址，避免程序崩溃
4. **资源清理**：程序结束前调用 `wnbios_cleanup_driver()`

## 🔧 编译和运行

```bash
# 编译项目
cd test
build.bat

# 运行示例
cd dist/examples
c_example.exe
```

## 📚 更多信息

- 完整API文档：`docs/API_Documentation.md`
- 示例代码：`examples/` 目录
- 头文件：`include/wnbios_dll.h` 和 `include/wnbios_types.h`