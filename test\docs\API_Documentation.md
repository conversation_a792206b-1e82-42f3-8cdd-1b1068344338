# WNBIOS_DLL API 文档

## 📖 概述

**wnbios_dll** 是一个基于 Windows 内核驱动的内存操作库，提供了进程内存读写、模块管理、错误处理等功能。该库使用 C API 接口，支持跨进程内存访问，适用于游戏外挂开发、系统监控、内存调试等场景。

### 🎯 主要特性

- **内核级访问**：通过嵌入式驱动实现内核级内存访问
- **线程安全**：使用互斥锁保护关键操作
- **类型灵活**：支持读写任意数据类型
- **错误处理**：完善的错误码和错误信息系统
- **日志系统**：可配置的日志级别和回调机制

---

## 🔧 驱动管理 API

### `wnbios_init_driver`
初始化 wnbios 内核驱动。

```c
WNBIOS_API int WNBIOS_CALL wnbios_init_driver(void);
```

**返回值：**
- `WNBIOS_SUCCESS` (0)：成功
- `WNBIOS_ERROR_INIT` (-1)：初始化失败

**使用示例：**
```c
if (wnbios_init_driver() != WNBIOS_SUCCESS) {
    printf("驱动初始化失败\n");
    return -1;
}
```

### `wnbios_cleanup_driver`
清理驱动资源，释放内存。

```c
WNBIOS_API int WNBIOS_CALL wnbios_cleanup_driver(void);
```

**返回值：**
- `WNBIOS_SUCCESS` (0)：成功
- `WNBIOS_ERROR_DRIVER` (-2)：驱动错误

**使用示例：**
```c
// 程序结束前清理资源
wnbios_cleanup_driver();
```

### `wnbios_is_driver_loaded`
检查驱动是否已加载。

```c
WNBIOS_API int WNBIOS_CALL wnbios_is_driver_loaded(void);
```

**返回值：**
- `1`：驱动已加载
- `0`：驱动未加载
- 负数：错误

**使用示例：**
```c
if (wnbios_is_driver_loaded()) {
    printf("驱动已加载\n");
}
```

---

## 🎯 进程操作 API

### `wnbios_find_process_by_name`
根据进程名查找进程信息。

```c
WNBIOS_API int WNBIOS_CALL wnbios_find_process_by_name(
    const char* name, 
    WnbiosProcessInfo* process
);
```

**参数：**
- `name`：进程名称（如 "notepad.exe"）
- `process`：输出的进程信息结构

**返回值：**
- `WNBIOS_SUCCESS` (0)：成功
- `WNBIOS_ERROR_NOT_FOUND` (-9)：进程未找到
- `WNBIOS_ERROR_INVALID_ARG` (-6)：参数无效

**使用示例：**
```c
WnbiosProcessInfo process_info;
if (wnbios_find_process_by_name("notepad.exe", &process_info) == WNBIOS_SUCCESS) {
    printf("进程ID: %d\n", process_info.pid);
    printf("基址: 0x%llx\n", process_info.base_address);
}
```

### `wnbios_get_process_base`
获取进程基址。

```c
WNBIOS_API uintptr_t WNBIOS_CALL wnbios_get_process_base(const char* process_name);
```

**参数：**
- `process_name`：进程名称

**返回值：**
- 成功：进程基址（非零值）
- 失败：0

**使用示例：**
```c
uintptr_t base_addr = wnbios_get_process_base("game.exe");
if (base_addr != 0) {
    printf("游戏基址: 0x%llx\n", base_addr);
}
```

### `wnbios_set_target_process`
设置目标进程，用于后续的内存操作。

```c
WNBIOS_API int WNBIOS_CALL wnbios_set_target_process(const char* process_name);
```

**参数：**
- `process_name`：目标进程名称

**返回值：**
- `WNBIOS_SUCCESS` (0)：成功
- `WNBIOS_ERROR_NOT_FOUND` (-9)：进程未找到

**使用示例：**
```c
if (wnbios_set_target_process("game.exe") == WNBIOS_SUCCESS) {
    printf("目标进程设置成功\n");
    // 现在可以进行内存操作
}
```

---

## 💾 内存操作 API

### `wnbios_read_memory`
读取目标进程的虚拟内存。

```c
WNBIOS_API int WNBIOS_CALL wnbios_read_memory(
    uintptr_t address, 
    void* buffer, 
    size_t size
);
```

**参数：**
- `address`：要读取的内存地址
- `buffer`：接收数据的缓冲区
- `size`：要读取的字节数

**返回值：**
- 成功：实际读取的字节数（正数）
- 失败：负数错误码

**使用示例：**
```c
// 读取32位整数
uint32_t value;
if (wnbios_read_memory(0x12345678, &value, sizeof(value)) > 0) {
    printf("读取的值: %u\n", value);
}

// 读取字符串
char buffer[256];
if (wnbios_read_memory(string_addr, buffer, sizeof(buffer) - 1) > 0) {
    buffer[255] = '\0';  // 确保字符串结束
    printf("字符串: %s\n", buffer);
}

// 读取结构体
typedef struct {
    float x, y, z;
    int health;
} Player;

Player player;
if (wnbios_read_memory(player_addr, &player, sizeof(player)) > 0) {
    printf("位置: (%.2f, %.2f, %.2f)\n", player.x, player.y, player.z);
    printf("生命值: %d\n", player.health);
}
```

### `wnbios_write_memory`
写入数据到目标进程的虚拟内存。

```c
WNBIOS_API int WNBIOS_CALL wnbios_write_memory(
    uintptr_t address, 
    const void* data, 
    size_t size
);
```

**参数：**
- `address`：要写入的内存地址
- `data`：要写入的数据
- `size`：要写入的字节数

**返回值：**
- 成功：实际写入的字节数（正数）
- 失败：负数错误码

**使用示例：**
```c
// 写入32位整数
uint32_t new_value = 9999;
if (wnbios_write_memory(health_addr, &new_value, sizeof(new_value)) > 0) {
    printf("生命值修改成功\n");
}

// 写入浮点数
float new_speed = 10.5f;
wnbios_write_memory(speed_addr, &new_speed, sizeof(new_speed));

// 写入字符串
char new_name[] = "Hacker";
wnbios_write_memory(name_addr, new_name, strlen(new_name) + 1);
```

### `wnbios_read_memory_batch`
批量读取多个内存地址的数据。

```c
WNBIOS_API int WNBIOS_CALL wnbios_read_memory_batch(
    const uintptr_t* addresses, 
    void** buffers, 
    const size_t* sizes, 
    int count
);
```

**参数：**
- `addresses`：内存地址数组
- `buffers`：接收数据的缓冲区数组
- `sizes`：每个地址要读取的字节数数组
- `count`：地址数量

**返回值：**
- `WNBIOS_SUCCESS` (0)：全部成功
- `WNBIOS_ERROR_MEMORY` (-4)：部分或全部失败

**使用示例：**
```c
// 批量读取玩家数据
uintptr_t addresses[3] = {health_addr, armor_addr, score_addr};
uint32_t health, armor, score;
void* buffers[3] = {&health, &armor, &score};
size_t sizes[3] = {sizeof(health), sizeof(armor), sizeof(score)};

if (wnbios_read_memory_batch(addresses, buffers, sizes, 3) == WNBIOS_SUCCESS) {
    printf("生命值: %u, 护甲: %u, 分数: %u\n", health, armor, score);
}
```

---

## 📦 模块管理 API

### `wnbios_enum_modules`
枚举目标进程中加载的所有模块。

```c
WNBIOS_API int WNBIOS_CALL wnbios_enum_modules(
    const char* process_name, 
    WnbiosModuleInfo* modules, 
    int* count
);
```

**参数：**
- `process_name`：进程名称
- `modules`：模块信息数组缓冲区
- `count`：输入时为数组大小，输出时为实际模块数量

**返回值：**
- `WNBIOS_SUCCESS` (0)：成功
- `WNBIOS_ERROR_MODULE` (-5)：模块操作失败

**使用示例：**
```c
WnbiosModuleInfo modules[100];
int module_count = 100;

if (wnbios_enum_modules("game.exe", modules, &module_count) == WNBIOS_SUCCESS) {
    printf("找到 %d 个模块:\n", module_count);
    for (int i = 0; i < module_count; i++) {
        wprintf(L"模块: %s, 基址: 0x%llx, 大小: 0x%x\n", 
               modules[i].name, modules[i].base_address, modules[i].size);
    }
}
```

### `wnbios_find_module_base`
查找指定模块的基址。

```c
WNBIOS_API uintptr_t WNBIOS_CALL wnbios_find_module_base(
    const char* process_name, 
    const wchar_t* module_name
);
```

**参数：**
- `process_name`：进程名称
- `module_name`：模块名称（宽字符）

**返回值：**
- 成功：模块基址（非零值）
- 失败：0

**使用示例：**
```c
uintptr_t engine_base = wnbios_find_module_base("game.exe", L"engine.dll");
if (engine_base != 0) {
    printf("引擎模块基址: 0x%llx\n", engine_base);
    
    // 可以基于模块基址计算偏移
    uintptr_t player_addr = engine_base + 0x123456;
}
```

---

## ❌ 错误处理 API

### `wnbios_get_last_error`
获取最后一次操作的错误信息。

```c
WNBIOS_API int WNBIOS_CALL wnbios_get_last_error(WnbiosErrorInfo* error_info);
```

**参数：**
- `error_info`：输出的错误信息结构

**返回值：**
- `WNBIOS_SUCCESS` (0)：成功

**使用示例：**
```c
if (wnbios_read_memory(addr, &data, size) < 0) {
    WnbiosErrorInfo error;
    wnbios_get_last_error(&error);
    printf("错误: %s (函数: %s, 线程: %d)\n", 
           error.error_message, error.function_name, error.thread_id);
}
```

### `wnbios_clear_error`
清除错误状态。

```c
WNBIOS_API void WNBIOS_CALL wnbios_clear_error(void);
```

**使用示例：**
```c
wnbios_clear_error();  // 清除之前的错误状态
```

### `wnbios_get_error_string`
获取错误码对应的描述字符串。

```c
WNBIOS_API const char* WNBIOS_CALL wnbios_get_error_string(int error_code);
```

**参数：**
- `error_code`：错误码

**返回值：**
- 错误描述字符串

**使用示例：**
```c
int result = wnbios_read_memory(addr, &data, size);
if (result < 0) {
    printf("错误: %s\n", wnbios_get_error_string(result));
}
```

---

## 📝 日志和调试 API

### `wnbios_set_log_callback`
设置日志回调函数。

```c
WNBIOS_API void WNBIOS_CALL wnbios_set_log_callback(WnbiosLogCallback callback);
```

**参数：**
- `callback`：日志回调函数

**回调函数类型：**
```c
typedef void (WNBIOS_CALL *WnbiosLogCallback)(WnbiosLogLevel level, const char* message);
```

**使用示例：**
```c
void log_callback(WnbiosLogLevel level, const char* message) {
    const char* level_str;
    switch (level) {
        case WNBIOS_LOG_ERROR: level_str = "ERROR"; break;
        case WNBIOS_LOG_WARNING: level_str = "WARN"; break;
        case WNBIOS_LOG_INFO: level_str = "INFO"; break;
        case WNBIOS_LOG_DEBUG: level_str = "DEBUG"; break;
        default: level_str = "UNKNOWN"; break;
    }
    printf("[%s] %s\n", level_str, message);
}

// 设置回调
wnbios_set_log_callback(log_callback);
```

### `wnbios_set_log_level`
设置日志级别。

```c
WNBIOS_API void WNBIOS_CALL wnbios_set_log_level(WnbiosLogLevel level);
```

**参数：**
- `level`：日志级别

**日志级别：**
- `WNBIOS_LOG_ERROR` (0)：只显示错误
- `WNBIOS_LOG_WARNING` (1)：显示警告及以上
- `WNBIOS_LOG_INFO` (2)：显示信息及以上
- `WNBIOS_LOG_DEBUG` (3)：显示所有日志

**使用示例：**
```c
wnbios_set_log_level(WNBIOS_LOG_INFO);  // 设置为信息级别
```

### `wnbios_set_debug_mode`
启用或禁用调试模式。

```c
WNBIOS_API void WNBIOS_CALL wnbios_set_debug_mode(int enable);
```

**参数：**
- `enable`：1 启用，0 禁用

**使用示例：**
```c
wnbios_set_debug_mode(1);  // 启用调试模式
```

---

## 🔍 实用工具 API

### `wnbios_get_version`
获取 DLL 版本信息。

```c
WNBIOS_API void WNBIOS_CALL wnbios_get_version(int* major, int* minor, int* patch);
```

**参数：**
- `major`：主版本号
- `minor`：次版本号
- `patch`：补丁版本号

**使用示例：**
```c
int major, minor, patch;
wnbios_get_version(&major, &minor, &patch);
printf("版本: %d.%d.%d\n", major, minor, patch);
```

### `wnbios_get_supported_versions`
获取支持的 Windows 版本信息。

```c
WNBIOS_API const char* WNBIOS_CALL wnbios_get_supported_versions(void);
```

**返回值：**
- 版本信息字符串

**使用示例：**
```c
printf("支持的系统: %s\n", wnbios_get_supported_versions());
```

---

## 📊 数据结构

### `WnbiosProcessInfo`
进程信息结构。

```c
typedef struct {
    char name[256];           // 进程名称
    DWORD pid;               // 进程ID
    uintptr_t base_address;  // 进程基址
    uintptr_t cr3;           // 页目录基址
} WnbiosProcessInfo;
```

### `WnbiosModuleInfo`
模块信息结构。

```c
typedef struct {
    wchar_t name[256];       // 模块名称（Unicode）
    uintptr_t base_address;  // 模块基址
    DWORD size;              // 模块大小
    uintptr_t entry_point;   // 入口点地址
} WnbiosModuleInfo;
```

### `WnbiosErrorInfo`
错误信息结构。

```c
typedef struct {
    int error_code;          // 错误码
    char error_message[512]; // 错误描述
    char function_name[128]; // 发生错误的函数名
    DWORD thread_id;         // 线程ID
} WnbiosErrorInfo;
```

---

## 🚀 便捷宏定义 (wnbios_types.h)

为了简化常用操作，库提供了一个额外的头文件 `wnbios_types.h`，包含便捷宏和游戏开发常用的数据结构。

### 包含方式
```c
#include "wnbios_dll.h"      // 主要API
#include "wnbios_types.h"    // 便捷宏（可选）
```

### 基本数据类型读取宏

| 宏名称 | 用途 | 示例 |
|--------|------|------|
| `READ_BYTE(addr, out)` | 读取8位无符号整数 | `READ_BYTE(addr, &byte_val)` |
| `READ_WORD(addr, out)` | 读取16位无符号整数 | `READ_WORD(addr, &word_val)` |
| `READ_DWORD(addr, out)` | 读取32位无符号整数 | `READ_DWORD(addr, &dword_val)` |
| `READ_QWORD(addr, out)` | 读取64位无符号整数 | `READ_QWORD(addr, &qword_val)` |
| `READ_FLOAT(addr, out)` | 读取单精度浮点数 | `READ_FLOAT(addr, &float_val)` |
| `READ_DOUBLE(addr, out)` | 读取双精度浮点数 | `READ_DOUBLE(addr, &double_val)` |
| `READ_POINTER(addr, out)` | 读取指针值 | `READ_POINTER(addr, &ptr_val)` |

**有符号整数读取宏：**
- `READ_INT8(addr, out)` - 读取8位有符号整数
- `READ_INT16(addr, out)` - 读取16位有符号整数
- `READ_INT32(addr, out)` - 读取32位有符号整数
- `READ_INT64(addr, out)` - 读取64位有符号整数

**使用示例：**
```c
#include "wnbios_types.h"

// 读取玩家生命值
uint32_t health;
if (READ_DWORD(player_addr + 0x10, &health)) {
    printf("生命值: %u\n", health);
}

// 读取坐标
float x, y, z;
READ_FLOAT(player_addr + 0x20, &x);
READ_FLOAT(player_addr + 0x24, &y);
READ_FLOAT(player_addr + 0x28, &z);
printf("位置: (%.2f, %.2f, %.2f)\n", x, y, z);
```

### 基本数据类型写入宏

| 宏名称 | 用途 | 示例 |
|--------|------|------|
| `WRITE_BYTE(addr, val)` | 写入8位无符号整数 | `WRITE_BYTE(addr, 255)` |
| `WRITE_WORD(addr, val)` | 写入16位无符号整数 | `WRITE_WORD(addr, 65535)` |
| `WRITE_DWORD(addr, val)` | 写入32位无符号整数 | `WRITE_DWORD(addr, 9999)` |
| `WRITE_QWORD(addr, val)` | 写入64位无符号整数 | `WRITE_QWORD(addr, 999999)` |
| `WRITE_FLOAT(addr, val)` | 写入单精度浮点数 | `WRITE_FLOAT(addr, 3.14f)` |
| `WRITE_DOUBLE(addr, val)` | 写入双精度浮点数 | `WRITE_DOUBLE(addr, 3.14159)` |
| `WRITE_POINTER(addr, val)` | 写入指针值 | `WRITE_POINTER(addr, new_ptr)` |

**使用示例：**
```c
// 修改玩家属性
WRITE_DWORD(health_addr, 9999);        // 无敌生命值
WRITE_FLOAT(speed_addr, 10.5f);        // 提升移动速度
WRITE_DWORD(money_addr, 999999);       // 修改金钱
```

### 字符串操作宏

```c
// 读取字符串
#define READ_STRING(addr, out, size)    // 读取ANSI字符串
#define READ_WSTRING(addr, out, size)   // 读取Unicode字符串

// 写入字符串
#define WRITE_STRING(addr, str, size)   // 写入字符串

// 使用示例
char player_name[64];
READ_STRING(name_addr, player_name, sizeof(player_name) - 1);
player_name[63] = '\0';  // 确保字符串结束

// 修改玩家名称
char new_name[] = "Hacker";
WRITE_STRING(name_addr, new_name, strlen(new_name) + 1);
```

### 结构体和数组操作宏

```c
// 结构体操作
#define READ_STRUCT(addr, out, type)    // 读取结构体
#define WRITE_STRUCT(addr, data, type)  // 写入结构体

// 数组操作
#define READ_ARRAY(addr, out, count, type)   // 读取数组
#define WRITE_ARRAY(addr, data, count, type) // 写入数组

// 使用示例
typedef struct {
    float x, y, z;
    int health;
    char name[32];
} Player;

// 读取整个玩家结构
Player player;
READ_STRUCT(player_addr, &player, Player);

// 写入新的玩家数据
Player new_player = {100.0f, 200.0f, 300.0f, 9999, "SuperPlayer"};
WRITE_STRUCT(player_addr, &new_player, Player);

// 读取分数数组
int scores[10];
READ_ARRAY(score_addr, scores, 10, int);
```

### 预定义的游戏数据结构

`wnbios_types.h` 提供了游戏开发中常用的数据结构：

```c
// 3D向量
typedef struct {
    float x, y, z;
} Vector3;

// 2D向量
typedef struct {
    float x, y;
} Vector2;

// 颜色结构
typedef struct {
    uint8_t r, g, b, a;
} Color32;

// 4x4矩阵
typedef struct {
    float m[4][4];
} Matrix4x4;

// 游戏实体
typedef struct {
    uint32_t id;
    Vector3 position;
    Vector3 rotation;
    Vector3 scale;
    uint32_t flags;
} GameEntity;

// 玩家信息
typedef struct {
    uint32_t player_id;
    char name[64];
    Vector3 position;
    float health;
    float armor;
    uint32_t score;
    uint32_t team;
} PlayerInfo;
```

**使用示例：**
```c
// 读取玩家完整信息
PlayerInfo player;
READ_STRUCT(player_base, &player, PlayerInfo);

printf("玩家: %s (ID: %u)\n", player.name, player.player_id);
printf("生命值: %.1f, 护甲: %.1f\n", player.health, player.armor);
printf("位置: (%.2f, %.2f, %.2f)\n", 
       player.position.x, player.position.y, player.position.z);
printf("分数: %u, 队伍: %u\n", player.score, player.team);

// 修改玩家位置
Vector3 new_position = {1000.0f, 2000.0f, 3000.0f};
WRITE_STRUCT(player_base + offsetof(PlayerInfo, position), &new_position, Vector3);
```

### 指针链读取辅助函数

对于游戏中常见的多级指针，提供了便捷的辅助函数：

```c
// 二级指针链
int read_pointer_chain_2(uintptr_t base, int offset1, int offset2, 
                        void* output, size_t size);

// 三级指针链
int read_pointer_chain_3(uintptr_t base, int offset1, int offset2, int offset3,
                        void* output, size_t size);

// 四级指针链
int read_pointer_chain_4(uintptr_t base, int offset1, int offset2, int offset3, int offset4,
                        void* output, size_t size);
```

**使用示例：**
```c
// 游戏中常见的指针链：Base -> Player -> Health
uintptr_t game_base = wnbios_get_process_base("game.exe");
float health;

// 传统方式（繁琐）
uintptr_t player_ptr;
if (READ_POINTER(game_base + 0x123456, &player_ptr)) {
    if (READ_FLOAT(player_ptr + 0x10, &health)) {
        printf("生命值: %.1f\n", health);
    }
}

// 使用指针链函数（简洁）
if (read_pointer_chain_2(game_base, 0x123456, 0x10, &health, sizeof(health))) {
    printf("生命值: %.1f\n", health);
}

// 更复杂的四级指针链：Base -> World -> Player -> Stats -> Health
if (read_pointer_chain_4(game_base, 0x100, 0x50, 0x20, 0x10, &health, sizeof(health))) {
    printf("生命值: %.1f\n", health);
}
```

### 完整的游戏外挂示例

```c
#include "wnbios_dll.h"
#include "wnbios_types.h"

int main() {
    // 初始化
    if (wnbios_init_driver() != WNBIOS_SUCCESS) {
        printf("驱动初始化失败\n");
        return -1;
    }
    
    if (wnbios_set_target_process("game.exe") != WNBIOS_SUCCESS) {
        printf("未找到游戏进程\n");
        wnbios_cleanup_driver();
        return -1;
    }
    
    // 获取基址
    uintptr_t game_base = wnbios_get_process_base("game.exe");
    uintptr_t player_base = game_base + 0x123456;  // 假设的偏移
    
    // 读取当前玩家数据
    PlayerInfo player;
    if (READ_STRUCT(player_base, &player, PlayerInfo)) {
        printf("当前玩家: %s\n", player.name);
        printf("生命值: %.1f/100\n", player.health);
        printf("位置: (%.2f, %.2f, %.2f)\n", 
               player.position.x, player.position.y, player.position.z);
    }
    
    // 修改玩家属性（外挂功能）
    WRITE_FLOAT(player_base + offsetof(PlayerInfo, health), 9999.0f);  // 无敌
    WRITE_FLOAT(player_base + offsetof(PlayerInfo, armor), 9999.0f);   // 满护甲
    WRITE_DWORD(player_base + offsetof(PlayerInfo, score), 999999);    // 高分数
    
    // 传送功能
    Vector3 teleport_pos = {1000.0f, 0.0f, 1000.0f};
    WRITE_STRUCT(player_base + offsetof(PlayerInfo, position), &teleport_pos, Vector3);
    
    printf("外挂功能已激活！\n");
    
    // 清理
    wnbios_cleanup_driver();
    return 0;
}
```

### 宏 vs 原生API 对比

| 操作 | 原生API方式 | 便捷宏方式 |
|------|-------------|------------|
| 读取32位整数 | `wnbios_read_memory(addr, &val, sizeof(uint32_t))` | `READ_DWORD(addr, &val)` |
| 写入浮点数 | `float f = 3.14f; wnbios_write_memory(addr, &f, sizeof(float))` | `WRITE_FLOAT(addr, 3.14f)` |
| 读取结构体 | `wnbios_read_memory(addr, &struct_val, sizeof(MyStruct))` | `READ_STRUCT(addr, &struct_val, MyStruct)` |
| 读取数组 | `wnbios_read_memory(addr, array, count * sizeof(int))` | `READ_ARRAY(addr, array, count, int)` |

### 两种使用方式的选择

#### 方式1：仅使用基础API (`wnbios_dll.h`)
```c
#include "wnbios_dll.h"

// 适合：
// - 需要完全控制内存操作的场景
// - 对代码大小有严格要求
// - 不需要频繁的内存操作
// - 自定义数据结构较多

uint32_t health;
if (wnbios_read_memory(health_addr, &health, sizeof(uint32_t)) > 0) {
    printf("生命值: %u\n", health);
}
```

#### 方式2：使用便捷宏 (`wnbios_dll.h` + `wnbios_types.h`)
```c
#include "wnbios_dll.h"
#include "wnbios_types.h"

// 适合：
// - 游戏外挂开发
// - 需要频繁内存操作
// - 快速原型开发
// - 使用常见游戏数据结构

uint32_t health;
READ_DWORD(health_addr, &health);
printf("生命值: %u\n", health);
```

### 注意事项

1. **可选使用**：`wnbios_types.h` 是完全可选的，不影响基础功能
2. **编译器兼容性**：便捷宏在Visual Studio 2022中完全兼容
3. **类型安全**：宏会自动计算数据类型大小，减少sizeof()错误
4. **性能**：宏在编译时展开，不会影响运行时性能
5. **学习曲线**：新手建议先学习基础API，熟悉后再使用便捷宏

---

## 🏷️ 错误码参考

| 错误码 | 常量名 | 描述 |
|--------|--------|------|
| 0 | `WNBIOS_SUCCESS` | 操作成功 |
| -1 | `WNBIOS_ERROR_INIT` | 初始化错误 |
| -2 | `WNBIOS_ERROR_DRIVER` | 驱动错误 |
| -3 | `WNBIOS_ERROR_PROCESS` | 进程错误 |
| -4 | `WNBIOS_ERROR_MEMORY` | 内存错误 |
| -5 | `WNBIOS_ERROR_MODULE` | 模块错误 |
| -6 | `WNBIOS_ERROR_INVALID_ARG` | 无效参数 |
| -7 | `WNBIOS_ERROR_BUFFER_SIZE` | 缓冲区大小错误 |
| -8 | `WNBIOS_ERROR_ACCESS` | 访问拒绝 |
| -9 | `WNBIOS_ERROR_NOT_FOUND` | 未找到 |
| -10 | `WNBIOS_ERROR_TIMEOUT` | 超时 |

---

## 💡 使用最佳实践

### 1. 初始化和清理
```c
int main() {
    // 初始化驱动
    if (wnbios_init_driver() != WNBIOS_SUCCESS) {
        return -1;
    }
    
    // 设置日志
    wnbios_set_log_callback(my_log_callback);
    wnbios_set_log_level(WNBIOS_LOG_INFO);
    
    // 你的代码...
    
    // 清理资源
    wnbios_cleanup_driver();
    return 0;
}
```

### 2. 错误处理
```c
int result = wnbios_read_memory(addr, &data, size);
if (result < 0) {
    WnbiosErrorInfo error;
    wnbios_get_last_error(&error);
    printf("读取失败: %s\n", error.error_message);
    return result;
}
```

### 3. 指针链读取
```c
// 游戏中常见的多级指针
uintptr_t base = wnbios_get_process_base("game.exe");
uintptr_t player_ptr;
Vector3 position;

// 方法1：逐级读取
if (READ_POINTER(base + 0x123456, &player_ptr) &&
    READ_STRUCT(player_ptr + 0x10, &position, Vector3)) {
    printf("玩家位置: (%.2f, %.2f, %.2f)\n", position.x, position.y, position.z);
}

// 方法2：使用便捷函数
if (read_pointer_chain_2(base, 0x123456, 0x10, &position, sizeof(position))) {
    printf("玩家位置: (%.2f, %.2f, %.2f)\n", position.x, position.y, position.z);
}
```

### 4. 批量操作
```c
// 一次性读取多个玩家属性
uintptr_t addrs[] = {player_base + 0x10, player_base + 0x14, player_base + 0x18};
float health, armor, speed;
void* buffers[] = {&health, &armor, &speed};
size_t sizes[] = {sizeof(float), sizeof(float), sizeof(float)};

if (wnbios_read_memory_batch(addrs, buffers, sizes, 3) == WNBIOS_SUCCESS) {
    printf("生命值: %.1f, 护甲: %.1f, 速度: %.1f\n", health, armor, speed);
}
```

---

## ⚠️ 注意事项

1. **权限要求**：需要管理员权限运行
2. **目标进程**：使用内存操作前必须先调用 `wnbios_set_target_process`
3. **地址有效性**：确保读写的内存地址有效，避免程序崩溃
4. **字符串处理**：读取字符串时要预留结束符空间
5. **资源清理**：程序结束前务必调用 `wnbios_cleanup_driver`
6. **线程安全**：库内部已实现线程安全，可在多线程环境使用

---

## 📁 示例代码

完整的示例代码位于 `examples/` 目录：

- `c_example.c` - 基本使用示例
- `data_types_example.c` - 各种数据类型读取示例
- `macro_usage_example.c` - 便捷宏使用示例

编译运行示例：
```bash
cd test
build.bat
cd dist/examples
c_example.exe
```

---

## 🔗 相关文件

- `include/wnbios_dll.h` - 主要API头文件
- `include/wnbios_types.h` - 便捷宏和类型定义
- `src/wnbios_dll.cpp` - 主要实现文件
- `src/wnbios_dll.def` - DLL导出定义

---

*本文档版本：1.0.0*  
*最后更新：2024年*